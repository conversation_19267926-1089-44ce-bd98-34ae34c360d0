package com.fit2081.nutritrack.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fit2081.nutritrack.data.repository.PatientRepository
import com.fit2081.nutritrack.data.repository.UserRepository
import com.fit2081.nutritrack.data.session.SessionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val patientRepository: PatientRepository,
    private val sessionManager: SessionManager
) : ViewModel() {

    private val _authState = MutableStateFlow<AuthState>(AuthState.Idle)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _currentUserId = MutableStateFlow<String?>(null)
    val currentUserId: StateFlow<String?> = _currentUserId.asStateFlow()

    private val _availableUserIds = MutableStateFlow<List<String>>(emptyList())
    val availableUserIds: StateFlow<List<String>> = _availableUserIds.asStateFlow()

    init {
        loadAvailableUserIds()
        checkExistingSession()
    }

    private fun checkExistingSession() {
        if (sessionManager.isSessionValid()) {
            val userId = sessionManager.getCurrentUserId()
            if (userId != null) {
                _currentUserId.value = userId
                _authState.value = AuthState.Success
            }
        } else {
            sessionManager.clearSession()
        }
    }

    private fun loadAvailableUserIds() {
        viewModelScope.launch {
            try {
                // Load CSV data if needed (first run)
                patientRepository.loadCsvDataIfNeeded()

                // Get available user IDs
                val userIds = patientRepository.getAllUserIds()
                _availableUserIds.value = userIds
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Failed to load user data: ${e.message}")
            }
        }
    }

    fun validateCredentials(userId: String, phoneNumber: String) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading

                // Check if patient exists with these credentials
                val patient = patientRepository.validatePatientCredentials(phoneNumber, userId)
                if (patient == null) {
                    _authState.value = AuthState.Error("Invalid credentials")
                    return@launch
                }

                // Check if user account exists
                val user = userRepository.getUserById(userId)
                if (user == null) {
                    // First time login - need to set up account
                    _authState.value = AuthState.FirstTimeLogin(userId)
                } else {
                    // Existing user - need password
                    _authState.value = AuthState.ExistingUserLogin(userId)
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Login failed: ${e.message}")
            }
        }
    }

    fun createUserAccount(userId: String, password: String) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading

                val user = userRepository.createUser(userId, password)
                userRepository.updateLastLogin(userId)

                // Save session
                sessionManager.saveUserSession(userId)

                _currentUserId.value = userId
                _authState.value = AuthState.Success
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Account creation failed: ${e.message}")
            }
        }
    }

    fun loginExistingUser(userId: String, password: String) {
        viewModelScope.launch {
            try {
                _authState.value = AuthState.Loading

                val user = userRepository.validateUserCredentials(userId, password)
                if (user == null) {
                    _authState.value = AuthState.Error("Invalid password")
                    return@launch
                }

                userRepository.updateLastLogin(userId)

                // Save session
                sessionManager.saveUserSession(userId)

                _currentUserId.value = userId
                _authState.value = AuthState.Success
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Login failed: ${e.message}")
            }
        }
    }

    fun logout() {
        sessionManager.clearSession()
        _currentUserId.value = null
        _authState.value = AuthState.Idle
    }

    fun clearError() {
        if (_authState.value is AuthState.Error) {
            _authState.value = AuthState.Idle
        }
    }
}

sealed class AuthState {
    object Idle : AuthState()
    object Loading : AuthState()
    object Success : AuthState()
    data class FirstTimeLogin(val userId: String) : AuthState()
    data class ExistingUserLogin(val userId: String) : AuthState()
    data class Error(val message: String) : AuthState()
}
