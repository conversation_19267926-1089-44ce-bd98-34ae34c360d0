package com.fit2081.nutritrack.data.repository

import com.fit2081.nutritrack.data.database.dao.UserDao
import com.fit2081.nutritrack.data.database.entities.UserEntity
import kotlinx.coroutines.flow.Flow
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(
    private val userDao: UserDao
) {

    suspend fun getUserById(userId: String): UserEntity? {
        return userDao.getUserById(userId)
    }

    fun getUserByIdFlow(userId: String): Flow<UserEntity?> {
        return userDao.getUserByIdFlow(userId)
    }

    suspend fun validateUserCredentials(userId: String, password: String): UserEntity? {
        val passwordHash = hashPassword(password)
        return userDao.validateUserCredentials(userId, passwordHash)
    }

    suspend fun createUser(userId: String, password: String): UserEntity {
        val passwordHash = hashPassword(password)
        val user = UserEntity(
            userId = userId,
            passwordHash = passwordHash,
            name = "", // Name not required during registration
            isFirstLogin = false
        )
        userDao.insertUser(user)
        return user
    }

    suspend fun updateUser(user: UserEntity) {
        userDao.updateUser(user)
    }

    suspend fun deleteUser(user: UserEntity) {
        userDao.deleteUser(user)
    }

    suspend fun updateLastLogin(userId: String) {
        userDao.updateLastLogin(userId, System.currentTimeMillis())
    }

    suspend fun markFirstLoginComplete(userId: String) {
        userDao.markFirstLoginComplete(userId)
    }

    private fun hashPassword(password: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(password.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
}
