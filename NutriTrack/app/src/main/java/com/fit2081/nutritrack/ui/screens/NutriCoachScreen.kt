package com.fit2081.nutritrack.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.fit2081.nutritrack.data.models.Fruit
import com.fit2081.nutritrack.data.models.FruitState
import com.fit2081.nutritrack.ui.components.NavigationItem
import com.fit2081.nutritrack.ui.components.NutriTrackNavigationBar
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import com.fit2081.nutritrack.ui.viewmodel.NutriCoachViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NutriCoachScreen(
    onNavigateToHome: () -> Unit,
    onNavigateToInsights: () -> Unit,
    onNavigateToNutriCoach: () -> Unit,
    onNavigateToSettings: () -> Unit,
    authViewModel: AuthViewModel = hiltViewModel(),
    nutriCoachViewModel: NutriCoachViewModel = hiltViewModel()
) {
    val currentUserId by authViewModel.currentUserId.collectAsState()
    val fruitState by nutriCoachViewModel.fruitState.collectAsState()
    val searchQuery by nutriCoachViewModel.searchQuery.collectAsState()
    val foodScoreData by nutriCoachViewModel.foodScoreData.collectAsState()
    val keyboardController = LocalSoftwareKeyboardController.current
    
    // Load user's food score data when screen loads
    LaunchedEffect(currentUserId) {
        currentUserId?.let { userId ->
            nutriCoachViewModel.loadFoodScoreData(userId)
        }
    }
    
    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            NutriTrackNavigationBar(
                selectedItem = NavigationItem.NUTRICOACH,
                onNavigateToHome = onNavigateToHome,
                onNavigateToInsights = onNavigateToInsights,
                onNavigateToNutriCoach = onNavigateToNutriCoach,
                onNavigateToSettings = onNavigateToSettings
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Title
            Text(
                text = "NutriCoach",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            
            // Check if fruit coaching is available
            val showFruitCoaching = nutriCoachViewModel.isFruitScoreNonOptimal()
            
            if (showFruitCoaching) {
                // Fruit coaching section
                FruitCoachingSection(
                    searchQuery = searchQuery,
                    fruitState = fruitState,
                    onSearchQueryChange = nutriCoachViewModel::updateSearchQuery,
                    onSearchFruit = { 
                        nutriCoachViewModel.searchFruit(searchQuery)
                        keyboardController?.hide()
                    },
                    onClearSearch = nutriCoachViewModel::clearFruitSearch
                )
            } else {
                // Show message when fruit score is optimal
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "🎉 恭喜！",
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "您的水果摄入评分很好！继续保持健康的饮食习惯。",
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun FruitCoachingSection(
    searchQuery: String,
    fruitState: FruitState,
    onSearchQueryChange: (String) -> Unit,
    onSearchFruit: () -> Unit,
    onClearSearch: () -> Unit
) {
    Column {
        // Fruit search section
        Text(
            text = "Fruit Name",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            OutlinedTextField(
                value = searchQuery,
                onValueChange = onSearchQueryChange,
                placeholder = { Text("banana") },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(24.dp),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                keyboardActions = KeyboardActions(onSearch = { onSearchFruit() }),
                singleLine = true
            )
            
            Button(
                onClick = onSearchFruit,
                modifier = Modifier.height(56.dp),
                shape = RoundedCornerShape(12.dp),
                enabled = searchQuery.isNotBlank()
            ) {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search",
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Details")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Fruit results section
        when (fruitState) {
            is FruitState.Idle -> {
                // Show instruction
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Text(
                        text = "输入水果名称来查看营养信息和建议",
                        modifier = Modifier.padding(16.dp),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            is FruitState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            is FruitState.Success -> {
                FruitDetailsCard(fruit = fruitState.fruit, onClearSearch = onClearSearch)
            }
            
            is FruitState.Error -> {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "未找到水果信息",
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = fruitState.message,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            style = MaterialTheme.typography.bodySmall,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        TextButton(onClick = onClearSearch) {
                            Text("重试")
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun FruitDetailsCard(
    fruit: Fruit,
    onClearSearch: () -> Unit
) {
    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
        // Family
        NutritionInfoCard(label = "family", value = fruit.family)
        
        // Calories
        NutritionInfoCard(label = "calories", value = fruit.nutritions.calories.toString())
        
        // Fat
        NutritionInfoCard(label = "fat", value = fruit.nutritions.fat.toString())
        
        // Sugar
        NutritionInfoCard(label = "sugar", value = fruit.nutritions.sugar.toString())
        
        // Carbohydrates
        NutritionInfoCard(label = "carbohydrates", value = fruit.nutritions.carbohydrates.toString())
        
        // Protein
        NutritionInfoCard(label = "protein", value = fruit.nutritions.protein.toString())
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Clear button
        Button(
            onClick = onClearSearch,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("搜索其他水果")
        }
    }
}

@Composable
private fun NutritionInfoCard(
    label: String,
    value: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = ": $value",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}
