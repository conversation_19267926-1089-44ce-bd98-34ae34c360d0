package com.fit2081.nutritrack

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.fit2081.nutritrack.ui.screens.DashboardScreen
import com.fit2081.nutritrack.ui.screens.InsightsScreen
import com.fit2081.nutritrack.ui.screens.LoginScreen
import com.fit2081.nutritrack.ui.screens.NewDashboardScreen
import com.fit2081.nutritrack.ui.screens.NewLoginScreen
import com.fit2081.nutritrack.ui.screens.NewQuestionnaireScreen
import com.fit2081.nutritrack.ui.screens.QuestionnaireScreen
import com.fit2081.nutritrack.ui.screens.SettingsScreen
import com.fit2081.nutritrack.ui.screens.WelcomeScreen
import com.fit2081.nutritrack.ui.theme.NutriTrackTheme
import com.fit2081.nutritrack.ui.viewmodel.AuthState
import com.fit2081.nutritrack.ui.viewmodel.AuthViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            // Use system settings as the default dark mode setting
            val systemInDarkTheme = isSystemInDarkTheme()
            // Whether to use dynamic colors, default is false, prioritizing our custom health color scheme
            var useDynamicColor by remember { mutableStateOf(false) }
            // Whether to use dark mode, default follows system settings
            var isDarkTheme by remember { mutableStateOf(systemInDarkTheme) }
            NutriTrackTheme(
                darkTheme = isDarkTheme,
                dynamicColor = useDynamicColor
            ) {
                val navController = rememberNavController()
                val authViewModel: AuthViewModel = hiltViewModel()
                val authState by authViewModel.authState.collectAsState()
                val currentUserId by authViewModel.currentUserId.collectAsState()

                // Determine start destination based on auth state
                val startDestination = when {
                    currentUserId != null && authState is AuthState.Success -> "dashboard"
                    else -> "welcome"
                }

                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    NavHost(
                        navController = navController,
                        startDestination = startDestination,
                        modifier = Modifier.padding(innerPadding)
                    ) {
                        composable("welcome") {
                            WelcomeScreen(onNavigateToLogin = {
                                navController.navigate("login")
                            })
                        }
                        composable("login") {
                            NewLoginScreen(onLoginSuccess = {
                                navController.navigate("questionnaire") {
                                    popUpTo("welcome") { inclusive = true }
                                }
                            })
                        }
                        composable("questionnaire"){
                            NewQuestionnaireScreen(onSaveSuccess = {
                                navController.navigate("dashboard") {
                                    popUpTo("login") { inclusive = true }
                                }
                            })
                        }
                        composable("dashboard") {
                            NewDashboardScreen(
                                onEditQuestionnaire = {
                                    navController.navigate("questionnaire")
                                },
                                onNavigateToHome = {
                                    // Already on dashboard
                                },
                                onNavigateToProfile = {
                                    navController.navigate("insights")
                                },
                                onNavigateToInfo = {
                                    navController.navigate("nutricoach")
                                },
                                onNavigateToSettings = {
                                    navController.navigate("settings")
                                }
                            )
                        }
                        composable("insights") {
                            InsightsScreen(
                                onNavigateToHome = {
                                    navController.navigate("dashboard")
                                },
                                onNavigateToInsights = {
                                    // Already on insights
                                },
                                onNavigateToNutriCoach = {
                                    navController.navigate("nutricoach")
                                },
                                onNavigateToSettings = {
                                    navController.navigate("settings")
                                }
                            )
                        }
                        composable("nutricoach") {
                            // Placeholder for NutriCoach screen
                            // Will be implemented in future versions
                            InsightsScreen(
                                onNavigateToHome = { navController.navigate("dashboard") },
                                onNavigateToInsights = { navController.navigate("insights") },
                                onNavigateToNutriCoach = { /* Already on nutricoach */ },
                                onNavigateToSettings = { navController.navigate("settings") }
                            )
                        }
                        composable("settings") {
                            SettingsScreen(
                                onNavigateToHome = { navController.navigate("dashboard") },
                                onNavigateToProfile = { navController.navigate("insights") },
                                onNavigateToInfo = { navController.navigate("nutricoach") },
                                onNavigateToSettings = { /* Already on settings */ },
                                onLogout = {
                                    navController.navigate("welcome") {
                                        popUpTo(0) { inclusive = true }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}