package com.fit2081.nutritrack.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.fit2081.nutritrack.data.database.dao.FoodIntakeDao
import com.fit2081.nutritrack.data.database.dao.PatientDao
import com.fit2081.nutritrack.data.database.dao.UserDao
import com.fit2081.nutritrack.data.database.entities.FoodIntakeEntity
import com.fit2081.nutritrack.data.database.entities.PatientEntity
import com.fit2081.nutritrack.data.database.entities.UserEntity

@Database(
    entities = [
        PatientEntity::class,
        UserEntity::class,
        FoodIntakeEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class NutriTrackDatabase : RoomDatabase() {
    
    abstract fun patientDao(): PatientDao
    abstract fun userDao(): UserDao
    abstract fun foodIntakeDao(): FoodIntakeDao
    
    companion object {
        @Volatile
        private var INSTANCE: NutriTrackDatabase? = null
        
        fun getDatabase(context: Context): NutriTrackDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    NutriTrackDatabase::class.java,
                    "nutritrack_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
