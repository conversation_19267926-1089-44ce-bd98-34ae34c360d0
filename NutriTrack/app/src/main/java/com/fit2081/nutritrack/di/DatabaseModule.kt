package com.fit2081.nutritrack.di

import android.content.Context
import androidx.room.Room
import com.fit2081.nutritrack.data.database.NutriTrackDatabase
import com.fit2081.nutritrack.data.database.dao.FoodIntakeDao
import com.fit2081.nutritrack.data.database.dao.PatientDao
import com.fit2081.nutritrack.data.database.dao.UserDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideNutriTrackDatabase(@ApplicationContext context: Context): NutriTrackDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            NutriTrackDatabase::class.java,
            "nutritrack_database"
        ).build()
    }
    
    @Provides
    fun providePatientDao(database: NutriTrackDatabase): PatientDao {
        return database.patientDao()
    }
    
    @Provides
    fun provideUserDao(database: NutriTrackDatabase): UserDao {
        return database.userDao()
    }
    
    @Provides
    fun provideFoodIntakeDao(database: NutriTrackDatabase): FoodIntakeDao {
        return database.foodIntakeDao()
    }
}
